import React from 'react';
import { Box, Typography, Chip } from '@mui/material';
import { Construction as ConstructionIcon } from '@mui/icons-material';

/**
 * Componente che mostra il cantiere selezionato
 */
const SelectedCantiereDisplay = () => {
  // Recupera l'ID e il nome del cantiere selezionato dal localStorage
  const selectedCantiereId = localStorage.getItem('selectedCantiereId');
  const selectedCantiereName = localStorage.getItem('selectedCantiereName');

  // Se non c'è un cantiere selezionato, non mostrare nulla
  if (!selectedCantiereId) {
    return null;
  }

  // Rimuove il "#n" dal nome del cantiere se presente
  const cleanCantiereName = (selectedCantiereName || selectedCantiereId || '').replace(/#\d+/g, '').trim();

  return (
    <Box sx={{ display: 'flex', alignItems: 'center', mx: 2 }}>
      <Typography variant="body2" color="textSecondary" sx={{ mr: 1.5, fontSize: '0.875rem', fontWeight: 500 }}>
        Cantiere:
      </Typography>
      <Chip
        icon={<ConstructionIcon fontSize="small" />}
        label={cleanCantiereName}
        color="secondary"
        variant="outlined"
        size="small"
        sx={{
          fontWeight: 'bold',
          fontSize: '0.875rem',
          padding: '4px 0',
          height: '28px',
          '& .MuiChip-label': { padding: '0 10px' }
        }}
      />
    </Box>
  );
};

export default SelectedCantiereDisplay;
