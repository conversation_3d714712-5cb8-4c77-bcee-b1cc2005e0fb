{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_0d3699dc._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_594fdf6a.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api|_next\\/static|_next\\/image|favicon.ico|public).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico|public).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "DmFlnj8I3P1ljkWGhQVgM+UNdCE3hT3/mJhLA6xJySg=", "__NEXT_PREVIEW_MODE_ID": "eee0875642299f66be4d7885de13d722", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "7c5ecff6c7e62617b3b36404223d0d46d5962b44dcfe4e74e09a9076815c17ac", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "8a0aceec8e82a3b0241e5c4d1988e7a86622f0e9819432ffab278aa882cd00d5"}}}, "sortedMiddleware": ["/"], "functions": {}}