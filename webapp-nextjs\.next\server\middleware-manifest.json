{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_0d3699dc._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_594fdf6a.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api|_next\\/static|_next\\/image|favicon.ico|public).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico|public).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "DmFlnj8I3P1ljkWGhQVgM+UNdCE3hT3/mJhLA6xJySg=", "__NEXT_PREVIEW_MODE_ID": "abd7c436b07c38503fd478a77d382551", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "f5229c32da40d28f3f9009ccfab53e1c8d4b7d647fc5c923b8f44e93c2e90dad", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "72cdb0dad89c4b0c30c54b1f8d9e10030e1a483fa3e5196da51944f9d63e55f2"}}}, "sortedMiddleware": ["/"], "functions": {}}