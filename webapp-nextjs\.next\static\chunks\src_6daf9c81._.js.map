{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 26, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\n        ghost:\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n        icon: \"size-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nfunction Button({\n  className,\n  variant,\n  size,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"button\"> &\n  VariantProps<typeof buttonVariants> & {\n    asChild?: boolean\n  }) {\n  const Comp = asChild ? Slot : \"button\"\n\n  return (\n    <Comp\n      data-slot=\"button\"\n      className={cn(buttonVariants({ variant, size, className }))}\n      {...props}\n    />\n  )\n}\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,6LAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf;KAnBS", "debugId": null}}, {"offset": {"line": 89, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/ui/animated-button.tsx"], "sourcesContent": ["import React from 'react'\nimport { cn } from '@/lib/utils'\nimport { Loader2 } from 'lucide-react'\n\ninterface AnimatedButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {\n  variant?: 'primary' | 'secondary' | 'success' | 'danger' | 'outline' | 'quick'\n  size?: 'sm' | 'md' | 'lg'\n  loading?: boolean\n  glow?: boolean\n  icon?: React.ReactNode\n  children: React.ReactNode\n}\n\nexport const AnimatedButton = React.forwardRef<HTMLButtonElement, AnimatedButtonProps>(\n  ({ \n    className, \n    variant = 'primary', \n    size = 'md', \n    loading = false, \n    glow = false,\n    icon,\n    children, \n    disabled,\n    ...props \n  }, ref) => {\n    const baseClasses = 'relative overflow-hidden font-medium rounded-lg transition-all duration-300 ease-in-out transform focus:outline-none'\n    \n    const variantClasses = {\n      primary: 'btn-primary',\n      secondary: 'btn-secondary',\n      success: 'btn-success',\n      danger: 'btn-danger',\n      outline: 'btn-outline',\n      quick: 'btn-quick'\n    }\n    \n    const sizeClasses = {\n      sm: 'btn-sm',\n      md: 'px-6 py-3',\n      lg: 'btn-lg'\n    }\n\n    const isDisabled = disabled || loading\n\n    return (\n      <button\n        className={cn(\n          baseClasses,\n          variantClasses[variant],\n          sizeClasses[size],\n          glow && variant !== 'quick' && 'btn-glow',\n          isDisabled && 'opacity-50 cursor-not-allowed hover:shadow-none',\n          className\n        )}\n        disabled={isDisabled}\n        ref={ref}\n        {...props}\n      >\n        {/* Effetto shimmer per hover */}\n        <span className=\"absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent translate-x-[-100%] transition-transform duration-700 ease-in-out group-hover:translate-x-[100%]\" />\n        \n        {/* Contenuto del pulsante */}\n        <span className=\"relative flex items-center justify-center gap-2\">\n          {loading ? (\n            <Loader2 className=\"h-4 w-4 animate-spin btn-icon\" />\n          ) : icon ? (\n            <span className=\"btn-icon\">{icon}</span>\n          ) : null}\n          {children}\n        </span>\n      </button>\n    )\n  }\n)\n\nAnimatedButton.displayName = 'AnimatedButton'\n\n// Componenti specifici per facilità d'uso\nexport const PrimaryButton = (props: Omit<AnimatedButtonProps, 'variant'>) => (\n  <AnimatedButton variant=\"primary\" {...props} />\n)\n\nexport const SecondaryButton = (props: Omit<AnimatedButtonProps, 'variant'>) => (\n  <AnimatedButton variant=\"secondary\" {...props} />\n)\n\nexport const SuccessButton = (props: Omit<AnimatedButtonProps, 'variant'>) => (\n  <AnimatedButton variant=\"success\" {...props} />\n)\n\nexport const DangerButton = (props: Omit<AnimatedButtonProps, 'variant'>) => (\n  <AnimatedButton variant=\"danger\" {...props} />\n)\n\nexport const OutlineButton = (props: Omit<AnimatedButtonProps, 'variant'>) => (\n  <AnimatedButton variant=\"outline\" {...props} />\n)\n\nexport const QuickButton = (props: Omit<AnimatedButtonProps, 'variant'>) => (\n  <AnimatedButton variant=\"quick\" {...props} />\n)\n"], "names": [], "mappings": ";;;;;;;;;;AAAA;AACA;AACA;;;;;AAWO,MAAM,+BAAiB,6JAAA,CAAA,UAAK,CAAC,UAAU,CAC5C,CAAC,EACC,SAAS,EACT,UAAU,SAAS,EACnB,OAAO,IAAI,EACX,UAAU,KAAK,EACf,OAAO,KAAK,EACZ,IAAI,EACJ,QAAQ,EACR,QAAQ,EACR,GAAG,OACJ,EAAE;IACD,MAAM,cAAc;IAEpB,MAAM,iBAAiB;QACrB,SAAS;QACT,WAAW;QACX,SAAS;QACT,QAAQ;QACR,SAAS;QACT,OAAO;IACT;IAEA,MAAM,cAAc;QAClB,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,MAAM,aAAa,YAAY;IAE/B,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,aACA,cAAc,CAAC,QAAQ,EACvB,WAAW,CAAC,KAAK,EACjB,QAAQ,YAAY,WAAW,YAC/B,cAAc,mDACd;QAEF,UAAU;QACV,KAAK;QACJ,GAAG,KAAK;;0BAGT,6LAAC;gBAAK,WAAU;;;;;;0BAGhB,6LAAC;gBAAK,WAAU;;oBACb,wBACC,6LAAC,oNAAA,CAAA,UAAO;wBAAC,WAAU;;;;;+BACjB,qBACF,6LAAC;wBAAK,WAAU;kCAAY;;;;;+BAC1B;oBACH;;;;;;;;;;;;;AAIT;KA3DW;AA8Db,eAAe,WAAW,GAAG;AAGtB,MAAM,gBAAgB,CAAC,sBAC5B,6LAAC;QAAe,SAAQ;QAAW,GAAG,KAAK;;;;;;MADhC;AAIN,MAAM,kBAAkB,CAAC,sBAC9B,6LAAC;QAAe,SAAQ;QAAa,GAAG,KAAK;;;;;;MADlC;AAIN,MAAM,gBAAgB,CAAC,sBAC5B,6LAAC;QAAe,SAAQ;QAAW,GAAG,KAAK;;;;;;MADhC;AAIN,MAAM,eAAe,CAAC,sBAC3B,6LAAC;QAAe,SAAQ;QAAU,GAAG,KAAK;;;;;;MAD/B;AAIN,MAAM,gBAAgB,CAAC,sBAC5B,6LAAC;QAAe,SAAQ;QAAW,GAAG,KAAK;;;;;;MADhC;AAIN,MAAM,cAAc,CAAC,sBAC1B,6LAAC;QAAe,SAAQ;QAAS,GAAG,KAAK;;;;;;MAD9B", "debugId": null}}, {"offset": {"line": 239, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/lib/api.ts"], "sourcesContent": ["import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios'\n\n// Configurazione base per l'API\nconst API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8001'\n\n// Crea istanza axios con configurazione base\nconst apiClient: AxiosInstance = axios.create({\n  baseURL: API_BASE_URL,\n  timeout: 30000,\n  headers: {\n    'Content-Type': 'application/json',\n  },\n})\n\n// Interceptor per aggiungere il token di autenticazione\napiClient.interceptors.request.use(\n  (config) => {\n    // Verifica se siamo nel browser prima di accedere a localStorage\n    if (typeof window !== 'undefined') {\n      const token = localStorage.getItem('token')\n      if (token) {\n        config.headers.Authorization = `Bearer ${token}`\n      }\n    }\n    return config\n  },\n  (error) => {\n    return Promise.reject(error)\n  }\n)\n\n// Interceptor per gestire le risposte e gli errori\napiClient.interceptors.response.use(\n  (response: AxiosResponse) => {\n    return response\n  },\n  (error) => {\n    if (error.response?.status === 401 && typeof window !== 'undefined') {\n      // Token scaduto o non valido\n      localStorage.removeItem('token')\n      localStorage.removeItem('access_token')\n      localStorage.removeItem('user_data')\n      localStorage.removeItem('cantiere_data')\n      window.location.href = '/login'\n    }\n    return Promise.reject(error)\n  }\n)\n\n// Tipi per le risposte API\nexport interface ApiResponse<T = any> {\n  data: T\n  message?: string\n  status: number\n}\n\nexport interface PaginatedResponse<T> {\n  items: T[]\n  total: number\n  page: number\n  size: number\n  pages: number\n}\n\n// Funzioni helper per le chiamate API\nexport const api = {\n  // GET request\n  get: async <T = any>(url: string, config?: AxiosRequestConfig): Promise<T> => {\n    const response = await apiClient.get<T>(url, config)\n    return response.data\n  },\n\n  // POST request\n  post: async <T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> => {\n    const response = await apiClient.post<T>(url, data, config)\n    return response.data\n  },\n\n  // PUT request\n  put: async <T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> => {\n    const response = await apiClient.put<T>(url, data, config)\n    return response.data\n  },\n\n  // PATCH request\n  patch: async <T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<T> => {\n    const response = await apiClient.patch<T>(url, data, config)\n    return response.data\n  },\n\n  // DELETE request\n  delete: async <T = any>(url: string, config?: AxiosRequestConfig): Promise<T> => {\n    const response = await apiClient.delete<T>(url, config)\n    return response.data\n  },\n}\n\n// Servizi API specifici per CABLYS\nexport const authApi = {\n  // Login utente - usa FormData per OAuth2PasswordRequestForm\n  login: async (credentials: { username: string; password: string }) => {\n    const formData = new FormData()\n    formData.append('username', credentials.username)\n    formData.append('password', credentials.password)\n\n    const response = await apiClient.post('/api/auth/login', formData, {\n      headers: {\n        'Content-Type': 'application/x-www-form-urlencoded',\n      },\n    })\n    return response.data\n  },\n\n  // Login cantiere - usa JSON per CantiereLogin\n  loginCantiere: (credentials: { codice_cantiere: string; password_cantiere: string }) =>\n    api.post<{ access_token: string; token_type: string; cantiere: any }>('/api/auth/login/cantiere', {\n      codice_univoco: credentials.codice_cantiere,\n      password: credentials.password_cantiere\n    }),\n\n  // Verifica token\n  verifyToken: () =>\n    api.post<{ user: any }>('/api/auth/test-token'),\n\n  // Logout\n  logout: () => {\n    localStorage.removeItem('access_token')\n    localStorage.removeItem('user_data')\n    window.location.href = '/login'\n  }\n}\n\nexport const caviApi = {\n  // Ottieni tutti i cavi\n  getCavi: (cantiereId: number, params?: any) =>\n    api.get<any[]>(`/api/cavi/${cantiereId}`, { params }),\n\n  // Ottieni cavo specifico\n  getCavo: (cantiereId: number, idCavo: string) =>\n    api.get<any>(`/api/cavi/${cantiereId}/${idCavo}`),\n\n  // Crea nuovo cavo\n  createCavo: (cantiereId: number, cavo: any) =>\n    api.post<any>(`/api/cavi/${cantiereId}`, cavo),\n\n  // Aggiorna cavo\n  updateCavo: (cantiereId: number, idCavo: string, updates: any) =>\n    api.put<any>(`/api/cavi/${cantiereId}/${idCavo}`, updates),\n\n  // Elimina cavo\n  deleteCavo: (cantiereId: number, idCavo: string) =>\n    api.delete(`/api/cavi/${cantiereId}/${idCavo}`),\n\n  // Aggiorna metri posati\n  updateMetriPosati: (cantiereId: number, idCavo: string, metri: number) =>\n    api.patch<any>(`/api/cavi/${cantiereId}/${idCavo}/metri-posati`, { metri_posati: metri }),\n\n  // Aggiorna bobina\n  updateBobina: (cantiereId: number, idCavo: string, bobina: string) =>\n    api.patch<any>(`/api/cavi/${cantiereId}/${idCavo}/bobina`, { id_bobina: bobina }),\n\n  // Aggiorna collegamento\n  updateCollegamento: (cantiereId: number, idCavo: string, collegamento: number) =>\n    api.patch<any>(`/api/cavi/${cantiereId}/${idCavo}/collegamento`, { collegamenti: collegamento }),\n}\n\nexport const parcoCaviApi = {\n  // Ottieni tutte le bobine\n  getBobine: (cantiereId: number) =>\n    api.get<any[]>(`/api/parco-cavi/${cantiereId}`),\n\n  // Ottieni bobina specifica\n  getBobina: (cantiereId: number, idBobina: string) =>\n    api.get<any>(`/api/parco-cavi/${cantiereId}/${idBobina}`),\n\n  // Crea nuova bobina\n  createBobina: (cantiereId: number, bobina: any) =>\n    api.post<any>(`/api/parco-cavi/${cantiereId}`, bobina),\n\n  // Aggiorna bobina\n  updateBobina: (cantiereId: number, idBobina: string, updates: any) =>\n    api.put<any>(`/api/parco-cavi/${cantiereId}/${idBobina}`, updates),\n\n  // Elimina bobina\n  deleteBobina: (cantiereId: number, idBobina: string) =>\n    api.delete(`/api/parco-cavi/${cantiereId}/${idBobina}`),\n}\n\nexport const comandeApi = {\n  // Ottieni tutte le comande\n  getComande: (cantiereId: number) =>\n    api.get<any[]>(`/api/comande/${cantiereId}`),\n\n  // Ottieni comanda specifica\n  getComanda: (cantiereId: number, codiceComanda: string) =>\n    api.get<any>(`/api/comande/${cantiereId}/${codiceComanda}`),\n\n  // Crea nuova comanda\n  createComanda: (cantiereId: number, comanda: any) =>\n    api.post<any>(`/api/comande/${cantiereId}`, comanda),\n\n  // Aggiorna comanda\n  updateComanda: (cantiereId: number, codiceComanda: string, updates: any) =>\n    api.put<any>(`/api/comande/${cantiereId}/${codiceComanda}`, updates),\n\n  // Elimina comanda\n  deleteComanda: (cantiereId: number, codiceComanda: string) =>\n    api.delete(`/api/comande/${cantiereId}/${codiceComanda}`),\n\n  // Assegna cavi a comanda\n  assegnaCavi: (cantiereId: number, codiceComanda: string, caviIds: string[]) =>\n    api.post<any>(`/api/comande/${cantiereId}/${codiceComanda}/assegna-cavi`, { cavi_ids: caviIds }),\n}\n\nexport const responsabiliApi = {\n  // Ottieni tutti i responsabili\n  getResponsabili: (cantiereId: number) =>\n    api.get<any[]>(`/api/responsabili/${cantiereId}`),\n\n  // Crea nuovo responsabile\n  createResponsabile: (cantiereId: number, responsabile: any) =>\n    api.post<any>(`/api/responsabili/${cantiereId}`, responsabile),\n\n  // Aggiorna responsabile\n  updateResponsabile: (cantiereId: number, id: number, updates: any) =>\n    api.put<any>(`/api/responsabili/${cantiereId}/${id}`, updates),\n\n  // Elimina responsabile\n  deleteResponsabile: (cantiereId: number, id: number) =>\n    api.delete(`/api/responsabili/${cantiereId}/${id}`),\n}\n\nexport const reportsApi = {\n  // Report avanzamento\n  getReportAvanzamento: (cantiereId: number) =>\n    api.get<any>(`/api/reports/${cantiereId}/avanzamento`),\n\n  // Report BOQ\n  getReportBOQ: (cantiereId: number) =>\n    api.get<any>(`/api/reports/${cantiereId}/boq`),\n\n  // Report utilizzo bobine\n  getReportUtilizzoBobine: (cantiereId: number) =>\n    api.get<any>(`/api/reports/${cantiereId}/utilizzo-bobine`),\n\n  // Report progress\n  getReportProgress: (cantiereId: number) =>\n    api.get<any>(`/api/reports/${cantiereId}/progress`),\n}\n\nexport const cantieriApi = {\n  // Ottieni tutti i cantieri\n  getCantieri: () =>\n    api.get<any[]>('/api/cantieri'),\n\n  // Ottieni cantiere specifico\n  getCantiere: (id: number) =>\n    api.get<any>(`/api/cantieri/${id}`),\n\n  // Crea nuovo cantiere\n  createCantiere: (cantiere: any) =>\n    api.post<any>('/api/cantieri', cantiere),\n\n  // Aggiorna cantiere\n  updateCantiere: (id: number, updates: any) =>\n    api.put<any>(`/api/cantieri/${id}`, updates),\n}\n\nexport const usersApi = {\n  // Ottieni tutti gli utenti (solo admin)\n  getUsers: () =>\n    api.get<any[]>('/api/users'),\n\n  // Ottieni utente specifico\n  getUser: (id: number) =>\n    api.get<any>(`/api/users/${id}`),\n\n  // Crea nuovo utente\n  createUser: (user: any) =>\n    api.post<any>('/api/users', user),\n\n  // Aggiorna utente\n  updateUser: (id: number, updates: any) =>\n    api.put<any>(`/api/users/${id}`, updates),\n\n  // Elimina utente\n  deleteUser: (id: number) =>\n    api.delete(`/api/users/${id}`),\n\n  // Abilita/Disabilita utente\n  toggleUserStatus: (id: number) =>\n    api.get<any>(`/api/users/toggle/${id}`),\n\n  // Verifica utenti scaduti\n  checkExpiredUsers: () =>\n    api.get<any>('/api/users/check-expired'),\n\n  // Impersona utente\n  impersonateUser: (userId: number) =>\n    api.post<any>('/api/auth/impersonate', { user_id: userId }),\n\n  // Ottieni dati database raw\n  getDatabaseData: () =>\n    api.get<any>('/api/users/db-raw'),\n\n  // Reset database\n  resetDatabase: () =>\n    api.post<any>('/api/admin/reset-database'),\n}\n\nexport default apiClient\n"], "names": [], "mappings": ";;;;;;;;;;;;AAGqB;AAHrB;;AAEA,gCAAgC;AAChC,MAAM,eAAe,6DAAmC;AAExD,6CAA6C;AAC7C,MAAM,YAA2B,wIAAA,CAAA,UAAK,CAAC,MAAM,CAAC;IAC5C,SAAS;IACT,SAAS;IACT,SAAS;QACP,gBAAgB;IAClB;AACF;AAEA,wDAAwD;AACxD,UAAU,YAAY,CAAC,OAAO,CAAC,GAAG,CAChC,CAAC;IACC,iEAAiE;IACjE,wCAAmC;QACjC,MAAM,QAAQ,aAAa,OAAO,CAAC;QACnC,IAAI,OAAO;YACT,OAAO,OAAO,CAAC,aAAa,GAAG,CAAC,OAAO,EAAE,OAAO;QAClD;IACF;IACA,OAAO;AACT,GACA,CAAC;IACC,OAAO,QAAQ,MAAM,CAAC;AACxB;AAGF,mDAAmD;AACnD,UAAU,YAAY,CAAC,QAAQ,CAAC,GAAG,CACjC,CAAC;IACC,OAAO;AACT,GACA,CAAC;IACC,IAAI,MAAM,QAAQ,EAAE,WAAW,OAAO,aAAkB,aAAa;QACnE,6BAA6B;QAC7B,aAAa,UAAU,CAAC;QACxB,aAAa,UAAU,CAAC;QACxB,aAAa,UAAU,CAAC;QACxB,aAAa,UAAU,CAAC;QACxB,OAAO,QAAQ,CAAC,IAAI,GAAG;IACzB;IACA,OAAO,QAAQ,MAAM,CAAC;AACxB;AAmBK,MAAM,MAAM;IACjB,cAAc;IACd,KAAK,OAAgB,KAAa;QAChC,MAAM,WAAW,MAAM,UAAU,GAAG,CAAI,KAAK;QAC7C,OAAO,SAAS,IAAI;IACtB;IAEA,eAAe;IACf,MAAM,OAAgB,KAAa,MAAY;QAC7C,MAAM,WAAW,MAAM,UAAU,IAAI,CAAI,KAAK,MAAM;QACpD,OAAO,SAAS,IAAI;IACtB;IAEA,cAAc;IACd,KAAK,OAAgB,KAAa,MAAY;QAC5C,MAAM,WAAW,MAAM,UAAU,GAAG,CAAI,KAAK,MAAM;QACnD,OAAO,SAAS,IAAI;IACtB;IAEA,gBAAgB;IAChB,OAAO,OAAgB,KAAa,MAAY;QAC9C,MAAM,WAAW,MAAM,UAAU,KAAK,CAAI,KAAK,MAAM;QACrD,OAAO,SAAS,IAAI;IACtB;IAEA,iBAAiB;IACjB,QAAQ,OAAgB,KAAa;QACnC,MAAM,WAAW,MAAM,UAAU,MAAM,CAAI,KAAK;QAChD,OAAO,SAAS,IAAI;IACtB;AACF;AAGO,MAAM,UAAU;IACrB,4DAA4D;IAC5D,OAAO,OAAO;QACZ,MAAM,WAAW,IAAI;QACrB,SAAS,MAAM,CAAC,YAAY,YAAY,QAAQ;QAChD,SAAS,MAAM,CAAC,YAAY,YAAY,QAAQ;QAEhD,MAAM,WAAW,MAAM,UAAU,IAAI,CAAC,mBAAmB,UAAU;YACjE,SAAS;gBACP,gBAAgB;YAClB;QACF;QACA,OAAO,SAAS,IAAI;IACtB;IAEA,8CAA8C;IAC9C,eAAe,CAAC,cACd,IAAI,IAAI,CAA8D,4BAA4B;YAChG,gBAAgB,YAAY,eAAe;YAC3C,UAAU,YAAY,iBAAiB;QACzC;IAEF,iBAAiB;IACjB,aAAa,IACX,IAAI,IAAI,CAAgB;IAE1B,SAAS;IACT,QAAQ;QACN,aAAa,UAAU,CAAC;QACxB,aAAa,UAAU,CAAC;QACxB,OAAO,QAAQ,CAAC,IAAI,GAAG;IACzB;AACF;AAEO,MAAM,UAAU;IACrB,uBAAuB;IACvB,SAAS,CAAC,YAAoB,SAC5B,IAAI,GAAG,CAAQ,CAAC,UAAU,EAAE,YAAY,EAAE;YAAE;QAAO;IAErD,yBAAyB;IACzB,SAAS,CAAC,YAAoB,SAC5B,IAAI,GAAG,CAAM,CAAC,UAAU,EAAE,WAAW,CAAC,EAAE,QAAQ;IAElD,kBAAkB;IAClB,YAAY,CAAC,YAAoB,OAC/B,IAAI,IAAI,CAAM,CAAC,UAAU,EAAE,YAAY,EAAE;IAE3C,gBAAgB;IAChB,YAAY,CAAC,YAAoB,QAAgB,UAC/C,IAAI,GAAG,CAAM,CAAC,UAAU,EAAE,WAAW,CAAC,EAAE,QAAQ,EAAE;IAEpD,eAAe;IACf,YAAY,CAAC,YAAoB,SAC/B,IAAI,MAAM,CAAC,CAAC,UAAU,EAAE,WAAW,CAAC,EAAE,QAAQ;IAEhD,wBAAwB;IACxB,mBAAmB,CAAC,YAAoB,QAAgB,QACtD,IAAI,KAAK,CAAM,CAAC,UAAU,EAAE,WAAW,CAAC,EAAE,OAAO,aAAa,CAAC,EAAE;YAAE,cAAc;QAAM;IAEzF,kBAAkB;IAClB,cAAc,CAAC,YAAoB,QAAgB,SACjD,IAAI,KAAK,CAAM,CAAC,UAAU,EAAE,WAAW,CAAC,EAAE,OAAO,OAAO,CAAC,EAAE;YAAE,WAAW;QAAO;IAEjF,wBAAwB;IACxB,oBAAoB,CAAC,YAAoB,QAAgB,eACvD,IAAI,KAAK,CAAM,CAAC,UAAU,EAAE,WAAW,CAAC,EAAE,OAAO,aAAa,CAAC,EAAE;YAAE,cAAc;QAAa;AAClG;AAEO,MAAM,eAAe;IAC1B,0BAA0B;IAC1B,WAAW,CAAC,aACV,IAAI,GAAG,CAAQ,CAAC,gBAAgB,EAAE,YAAY;IAEhD,2BAA2B;IAC3B,WAAW,CAAC,YAAoB,WAC9B,IAAI,GAAG,CAAM,CAAC,gBAAgB,EAAE,WAAW,CAAC,EAAE,UAAU;IAE1D,oBAAoB;IACpB,cAAc,CAAC,YAAoB,SACjC,IAAI,IAAI,CAAM,CAAC,gBAAgB,EAAE,YAAY,EAAE;IAEjD,kBAAkB;IAClB,cAAc,CAAC,YAAoB,UAAkB,UACnD,IAAI,GAAG,CAAM,CAAC,gBAAgB,EAAE,WAAW,CAAC,EAAE,UAAU,EAAE;IAE5D,iBAAiB;IACjB,cAAc,CAAC,YAAoB,WACjC,IAAI,MAAM,CAAC,CAAC,gBAAgB,EAAE,WAAW,CAAC,EAAE,UAAU;AAC1D;AAEO,MAAM,aAAa;IACxB,2BAA2B;IAC3B,YAAY,CAAC,aACX,IAAI,GAAG,CAAQ,CAAC,aAAa,EAAE,YAAY;IAE7C,4BAA4B;IAC5B,YAAY,CAAC,YAAoB,gBAC/B,IAAI,GAAG,CAAM,CAAC,aAAa,EAAE,WAAW,CAAC,EAAE,eAAe;IAE5D,qBAAqB;IACrB,eAAe,CAAC,YAAoB,UAClC,IAAI,IAAI,CAAM,CAAC,aAAa,EAAE,YAAY,EAAE;IAE9C,mBAAmB;IACnB,eAAe,CAAC,YAAoB,eAAuB,UACzD,IAAI,GAAG,CAAM,CAAC,aAAa,EAAE,WAAW,CAAC,EAAE,eAAe,EAAE;IAE9D,kBAAkB;IAClB,eAAe,CAAC,YAAoB,gBAClC,IAAI,MAAM,CAAC,CAAC,aAAa,EAAE,WAAW,CAAC,EAAE,eAAe;IAE1D,yBAAyB;IACzB,aAAa,CAAC,YAAoB,eAAuB,UACvD,IAAI,IAAI,CAAM,CAAC,aAAa,EAAE,WAAW,CAAC,EAAE,cAAc,aAAa,CAAC,EAAE;YAAE,UAAU;QAAQ;AAClG;AAEO,MAAM,kBAAkB;IAC7B,+BAA+B;IAC/B,iBAAiB,CAAC,aAChB,IAAI,GAAG,CAAQ,CAAC,kBAAkB,EAAE,YAAY;IAElD,0BAA0B;IAC1B,oBAAoB,CAAC,YAAoB,eACvC,IAAI,IAAI,CAAM,CAAC,kBAAkB,EAAE,YAAY,EAAE;IAEnD,wBAAwB;IACxB,oBAAoB,CAAC,YAAoB,IAAY,UACnD,IAAI,GAAG,CAAM,CAAC,kBAAkB,EAAE,WAAW,CAAC,EAAE,IAAI,EAAE;IAExD,uBAAuB;IACvB,oBAAoB,CAAC,YAAoB,KACvC,IAAI,MAAM,CAAC,CAAC,kBAAkB,EAAE,WAAW,CAAC,EAAE,IAAI;AACtD;AAEO,MAAM,aAAa;IACxB,qBAAqB;IACrB,sBAAsB,CAAC,aACrB,IAAI,GAAG,CAAM,CAAC,aAAa,EAAE,WAAW,YAAY,CAAC;IAEvD,aAAa;IACb,cAAc,CAAC,aACb,IAAI,GAAG,CAAM,CAAC,aAAa,EAAE,WAAW,IAAI,CAAC;IAE/C,yBAAyB;IACzB,yBAAyB,CAAC,aACxB,IAAI,GAAG,CAAM,CAAC,aAAa,EAAE,WAAW,gBAAgB,CAAC;IAE3D,kBAAkB;IAClB,mBAAmB,CAAC,aAClB,IAAI,GAAG,CAAM,CAAC,aAAa,EAAE,WAAW,SAAS,CAAC;AACtD;AAEO,MAAM,cAAc;IACzB,2BAA2B;IAC3B,aAAa,IACX,IAAI,GAAG,CAAQ;IAEjB,6BAA6B;IAC7B,aAAa,CAAC,KACZ,IAAI,GAAG,CAAM,CAAC,cAAc,EAAE,IAAI;IAEpC,sBAAsB;IACtB,gBAAgB,CAAC,WACf,IAAI,IAAI,CAAM,iBAAiB;IAEjC,oBAAoB;IACpB,gBAAgB,CAAC,IAAY,UAC3B,IAAI,GAAG,CAAM,CAAC,cAAc,EAAE,IAAI,EAAE;AACxC;AAEO,MAAM,WAAW;IACtB,wCAAwC;IACxC,UAAU,IACR,IAAI,GAAG,CAAQ;IAEjB,2BAA2B;IAC3B,SAAS,CAAC,KACR,IAAI,GAAG,CAAM,CAAC,WAAW,EAAE,IAAI;IAEjC,oBAAoB;IACpB,YAAY,CAAC,OACX,IAAI,IAAI,CAAM,cAAc;IAE9B,kBAAkB;IAClB,YAAY,CAAC,IAAY,UACvB,IAAI,GAAG,CAAM,CAAC,WAAW,EAAE,IAAI,EAAE;IAEnC,iBAAiB;IACjB,YAAY,CAAC,KACX,IAAI,MAAM,CAAC,CAAC,WAAW,EAAE,IAAI;IAE/B,4BAA4B;IAC5B,kBAAkB,CAAC,KACjB,IAAI,GAAG,CAAM,CAAC,kBAAkB,EAAE,IAAI;IAExC,0BAA0B;IAC1B,mBAAmB,IACjB,IAAI,GAAG,CAAM;IAEf,mBAAmB;IACnB,iBAAiB,CAAC,SAChB,IAAI,IAAI,CAAM,yBAAyB;YAAE,SAAS;QAAO;IAE3D,4BAA4B;IAC5B,iBAAiB,IACf,IAAI,GAAG,CAAM;IAEf,iBAAiB;IACjB,eAAe,IACb,IAAI,IAAI,CAAM;AAClB;uCAEe", "debugId": null}}, {"offset": {"line": 463, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/contexts/AuthContext.tsx"], "sourcesContent": ["'use client'\n\nimport React, { createContext, useContext, useState, useEffect, ReactNode } from 'react'\nimport { User, Cantier<PERSON> } from '@/types'\nimport { authApi, usersApi } from '@/lib/api'\n\ninterface AuthContextType {\n  user: User | null\n  cantiere: Cantiere | null\n  isAuthenticated: boolean\n  isLoading: boolean\n  isImpersonating: boolean\n  impersonatedUser: any | null\n  login: (username: string, password: string) => Promise<void>\n  loginCantiere: (codice_cantiere: string, password_cantiere: string) => Promise<void>\n  logout: () => void\n  checkAuth: () => Promise<void>\n  impersonateUser: (userId: number) => Promise<any>\n}\n\nconst AuthContext = createContext<AuthContextType | undefined>(undefined)\n\nexport function useAuth() {\n  const context = useContext(AuthContext)\n  if (context === undefined) {\n    throw new Error('useAuth must be used within an AuthProvider')\n  }\n  return context\n}\n\ninterface AuthProviderProps {\n  children: ReactNode\n}\n\nexport function AuthProvider({ children }: AuthProviderProps) {\n  const [user, setUser] = useState<User | null>(null)\n  const [cantiere, setCantiere] = useState<Cantiere | null>(null)\n  const [isLoading, setIsLoading] = useState(true)\n  const [isImpersonating, setIsImpersonating] = useState(() => {\n    if (typeof window !== 'undefined') {\n      return localStorage.getItem('isImpersonating') === 'true'\n    }\n    return false\n  })\n  const [impersonatedUser, setImpersonatedUser] = useState<any | null>(() => {\n    if (typeof window !== 'undefined') {\n      const stored = localStorage.getItem('impersonatedUser')\n      return stored ? JSON.parse(stored) : null\n    }\n    return null\n  })\n\n  const isAuthenticated = !!user || !!cantiere\n\n  // Verifica l'autenticazione al caricamento\n  useEffect(() => {\n    checkAuth()\n  }, [])\n\n  const checkAuth = async () => {\n    try {\n      console.log('Verificando autenticazione all\\'avvio...')\n      // Verifica se siamo nel browser\n      if (typeof window === 'undefined') {\n        setIsLoading(false)\n        return\n      }\n\n      // Prima di tutto, imposta loading a true\n      setIsLoading(true)\n\n      // Pulisci eventuali token non validi o scaduti\n      const token = localStorage.getItem('token')\n      console.log('Token trovato nel localStorage:', token ? 'Sì' : 'No')\n\n      if (token) {\n        try {\n          // Verifica la validità del token\n          console.log('Tentativo di verifica token...')\n          const userData = await authApi.verifyToken()\n          console.log('Token valido, dati utente:', userData)\n\n          // Imposta i dati dell'utente come nel sistema React originale\n          const userInfo = {\n            id_utente: userData.user_id,\n            username: userData.username,\n            ruolo: userData.role\n          }\n          setUser(userInfo)\n\n          // Gestisci l'impersonificazione\n          const impersonatingState = userData.is_impersonated === true\n          console.log('Stato di impersonificazione recuperato dai dati utente:', impersonatingState)\n          setIsImpersonating(impersonatingState)\n\n          if (impersonatingState && userData.impersonated_id) {\n            const impersonatedUserData = {\n              id: userData.impersonated_id,\n              username: userData.impersonated_username,\n              role: userData.impersonated_role\n            }\n            setImpersonatedUser(impersonatedUserData)\n            if (typeof window !== 'undefined') {\n              localStorage.setItem('impersonatedUser', JSON.stringify(impersonatedUserData))\n              localStorage.setItem('isImpersonating', 'true')\n            }\n          } else {\n            setImpersonatedUser(null)\n            if (typeof window !== 'undefined') {\n              localStorage.removeItem('impersonatedUser')\n              localStorage.removeItem('isImpersonating')\n            }\n          }\n\n          // Se è un utente cantiere, gestisci i dati del cantiere\n          if (userData.role === 'cantieri_user' && userData.cantiere_id) {\n            const cantiereData = {\n              id_cantiere: userData.cantiere_id,\n              commessa: userData.cantiere_name || `Cantiere ${userData.cantiere_id}`,\n              codice_univoco: '',\n              id_utente: userData.user_id\n            }\n            setCantiere(cantiereData)\n          }\n        } catch (tokenError) {\n          console.error('Errore durante la verifica del token:', tokenError)\n          // Se il token non è valido, rimuovilo\n          console.log('Rimozione token non valido dal localStorage')\n          localStorage.removeItem('token')\n          localStorage.removeItem('access_token')\n          localStorage.removeItem('user_data')\n          localStorage.removeItem('cantiere_data')\n          setUser(null)\n          setCantiere(null)\n        }\n      } else {\n        console.log('Nessun token trovato, utente non autenticato')\n        setUser(null)\n        setCantiere(null)\n      }\n    } catch (error) {\n      console.error('Errore generale durante la verifica dell\\'autenticazione:', error)\n      // In caso di errore generale, assicurati che l'utente non sia autenticato\n      if (typeof window !== 'undefined') {\n        localStorage.removeItem('token')\n        localStorage.removeItem('access_token')\n        localStorage.removeItem('user_data')\n        localStorage.removeItem('cantiere_data')\n      }\n      setUser(null)\n      setCantiere(null)\n    } finally {\n      // Assicurati che loading sia impostato a false alla fine\n      console.log('Completata verifica autenticazione, loading:', false)\n      setTimeout(() => {\n        setIsLoading(false)\n      }, 500) // Aggiungi un piccolo ritardo come nel sistema React originale\n    }\n  }\n\n  const login = async (username: string, password: string) => {\n    try {\n      console.log('Tentativo di login utente:', username)\n      setIsLoading(true)\n      const response = await authApi.login({ username, password })\n      console.log('Risposta login ricevuta:', response)\n\n      if (typeof window !== 'undefined') {\n        // Salva il token come nel sistema React originale\n        localStorage.setItem('token', response.access_token)\n\n        // Il backend restituisce i dati dell'utente direttamente nella risposta\n        const userData = {\n          id_utente: response.user_id,\n          username: response.username,\n          ruolo: response.role\n        }\n\n        console.log('Impostazione dati utente:', userData)\n        setUser(userData)\n        setCantiere(null)\n\n        return userData\n      }\n    } catch (error) {\n      console.error('Errore login:', error)\n      throw error\n    } finally {\n      setIsLoading(false)\n    }\n  }\n\n  const loginCantiere = async (codice_cantiere: string, password_cantiere: string) => {\n    try {\n      console.log('Tentativo di login cantiere:', codice_cantiere)\n      setIsLoading(true)\n      const response = await authApi.loginCantiere({ codice_cantiere, password_cantiere })\n      console.log('Risposta login cantiere ricevuta:', response)\n\n      if (typeof window !== 'undefined') {\n        // Salva il token come nel sistema React originale\n        localStorage.setItem('token', response.access_token)\n\n        // Il backend restituisce i dati del cantiere direttamente nella risposta\n        const cantiereData = {\n          id_cantiere: response.cantiere_id,\n          commessa: response.cantiere_name,\n          codice_univoco: codice_cantiere,\n          id_utente: response.user_id\n        }\n\n        console.log('Impostazione dati cantiere:', cantiereData)\n        setCantiere(cantiereData)\n        setUser(null)\n\n        return cantiereData\n      }\n    } catch (error) {\n      console.error('Errore login cantiere:', error)\n      throw error\n    } finally {\n      setIsLoading(false)\n    }\n  }\n\n  const impersonateUser = async (userId: number) => {\n    try {\n      // Chiama l'endpoint di impersonificazione\n      const response = await usersApi.impersonateUser(userId)\n\n      if (typeof window !== 'undefined') {\n        // Salva il token nel localStorage\n        localStorage.setItem('token', response.access_token)\n\n        // Salva i dati dell'utente impersonato\n        const impersonatedUserData = {\n          id: response.impersonated_id,\n          username: response.impersonated_username,\n          role: response.impersonated_role\n        }\n\n        // Salva i dati dell'utente impersonato nel localStorage\n        localStorage.setItem('impersonatedUser', JSON.stringify(impersonatedUserData))\n        setImpersonatedUser(impersonatedUserData)\n\n        // Imposta lo stato di impersonificazione a true\n        setIsImpersonating(true)\n        localStorage.setItem('isImpersonating', 'true')\n\n        console.log('Impersonificazione attivata')\n        console.log('Utente impersonato:', impersonatedUserData.username, 'Ruolo:', impersonatedUserData.role)\n\n        return { impersonatedUser: impersonatedUserData }\n      }\n    } catch (error) {\n      console.error('Errore durante l\\'impersonificazione:', error)\n      throw error\n    }\n  }\n\n  const logout = () => {\n    console.log('Logout completo - uscita dal sistema')\n    if (typeof window !== 'undefined') {\n      // Logout sempre completo - rimuovi tutto\n      localStorage.clear() // Pulisce tutto il localStorage\n      sessionStorage.clear() // Pulisce anche sessionStorage\n\n      // Reset stati\n      setUser(null)\n      setCantiere(null)\n      setIsImpersonating(false)\n      setImpersonatedUser(null)\n\n      // Forza reload completo della pagina per evitare cache\n      window.location.replace('/login')\n    }\n  }\n\n  const value: AuthContextType = {\n    user,\n    cantiere,\n    isAuthenticated,\n    isLoading,\n    isImpersonating,\n    impersonatedUser,\n    login,\n    loginCantiere,\n    logout,\n    checkAuth,\n    impersonateUser,\n  }\n\n  return (\n    <AuthContext.Provider value={value}>\n      {children}\n    </AuthContext.Provider>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAEA;AAEA;;;AAJA;;;AAoBA,MAAM,4BAAc,CAAA,GAAA,6JAAA,CAAA,gBAAa,AAAD,EAA+B;AAExD,SAAS;;IACd,MAAM,UAAU,CAAA,GAAA,6JAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;GANgB;AAYT,SAAS,aAAa,EAAE,QAAQ,EAAqB;;IAC1D,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;IAC9C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAmB;IAC1D,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD;iCAAE;YACrD,wCAAmC;gBACjC,OAAO,aAAa,OAAO,CAAC,uBAAuB;YACrD;;QAEF;;IACA,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD;iCAAc;YACnE,wCAAmC;gBACjC,MAAM,SAAS,aAAa,OAAO,CAAC;gBACpC,OAAO,SAAS,KAAK,KAAK,CAAC,UAAU;YACvC;;QAEF;;IAEA,MAAM,kBAAkB,CAAC,CAAC,QAAQ,CAAC,CAAC;IAEpC,2CAA2C;IAC3C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR;QACF;iCAAG,EAAE;IAEL,MAAM,YAAY;QAChB,IAAI;YACF,QAAQ,GAAG,CAAC;YACZ,gCAAgC;YAChC,uCAAmC;;YAGnC;YAEA,yCAAyC;YACzC,aAAa;YAEb,+CAA+C;YAC/C,MAAM,QAAQ,aAAa,OAAO,CAAC;YACnC,QAAQ,GAAG,CAAC,mCAAmC,QAAQ,OAAO;YAE9D,IAAI,OAAO;gBACT,IAAI;oBACF,iCAAiC;oBACjC,QAAQ,GAAG,CAAC;oBACZ,MAAM,WAAW,MAAM,oHAAA,CAAA,UAAO,CAAC,WAAW;oBAC1C,QAAQ,GAAG,CAAC,8BAA8B;oBAE1C,8DAA8D;oBAC9D,MAAM,WAAW;wBACf,WAAW,SAAS,OAAO;wBAC3B,UAAU,SAAS,QAAQ;wBAC3B,OAAO,SAAS,IAAI;oBACtB;oBACA,QAAQ;oBAER,gCAAgC;oBAChC,MAAM,qBAAqB,SAAS,eAAe,KAAK;oBACxD,QAAQ,GAAG,CAAC,2DAA2D;oBACvE,mBAAmB;oBAEnB,IAAI,sBAAsB,SAAS,eAAe,EAAE;wBAClD,MAAM,uBAAuB;4BAC3B,IAAI,SAAS,eAAe;4BAC5B,UAAU,SAAS,qBAAqB;4BACxC,MAAM,SAAS,iBAAiB;wBAClC;wBACA,oBAAoB;wBACpB,wCAAmC;4BACjC,aAAa,OAAO,CAAC,oBAAoB,KAAK,SAAS,CAAC;4BACxD,aAAa,OAAO,CAAC,mBAAmB;wBAC1C;oBACF,OAAO;wBACL,oBAAoB;wBACpB,wCAAmC;4BACjC,aAAa,UAAU,CAAC;4BACxB,aAAa,UAAU,CAAC;wBAC1B;oBACF;oBAEA,wDAAwD;oBACxD,IAAI,SAAS,IAAI,KAAK,mBAAmB,SAAS,WAAW,EAAE;wBAC7D,MAAM,eAAe;4BACnB,aAAa,SAAS,WAAW;4BACjC,UAAU,SAAS,aAAa,IAAI,CAAC,SAAS,EAAE,SAAS,WAAW,EAAE;4BACtE,gBAAgB;4BAChB,WAAW,SAAS,OAAO;wBAC7B;wBACA,YAAY;oBACd;gBACF,EAAE,OAAO,YAAY;oBACnB,QAAQ,KAAK,CAAC,yCAAyC;oBACvD,sCAAsC;oBACtC,QAAQ,GAAG,CAAC;oBACZ,aAAa,UAAU,CAAC;oBACxB,aAAa,UAAU,CAAC;oBACxB,aAAa,UAAU,CAAC;oBACxB,aAAa,UAAU,CAAC;oBACxB,QAAQ;oBACR,YAAY;gBACd;YACF,OAAO;gBACL,QAAQ,GAAG,CAAC;gBACZ,QAAQ;gBACR,YAAY;YACd;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,6DAA6D;YAC3E,0EAA0E;YAC1E,wCAAmC;gBACjC,aAAa,UAAU,CAAC;gBACxB,aAAa,UAAU,CAAC;gBACxB,aAAa,UAAU,CAAC;gBACxB,aAAa,UAAU,CAAC;YAC1B;YACA,QAAQ;YACR,YAAY;QACd,SAAU;YACR,yDAAyD;YACzD,QAAQ,GAAG,CAAC,gDAAgD;YAC5D,WAAW;gBACT,aAAa;YACf,GAAG,KAAK,+DAA+D;;QACzE;IACF;IAEA,MAAM,QAAQ,OAAO,UAAkB;QACrC,IAAI;YACF,QAAQ,GAAG,CAAC,8BAA8B;YAC1C,aAAa;YACb,MAAM,WAAW,MAAM,oHAAA,CAAA,UAAO,CAAC,KAAK,CAAC;gBAAE;gBAAU;YAAS;YAC1D,QAAQ,GAAG,CAAC,4BAA4B;YAExC,wCAAmC;gBACjC,kDAAkD;gBAClD,aAAa,OAAO,CAAC,SAAS,SAAS,YAAY;gBAEnD,wEAAwE;gBACxE,MAAM,WAAW;oBACf,WAAW,SAAS,OAAO;oBAC3B,UAAU,SAAS,QAAQ;oBAC3B,OAAO,SAAS,IAAI;gBACtB;gBAEA,QAAQ,GAAG,CAAC,6BAA6B;gBACzC,QAAQ;gBACR,YAAY;gBAEZ,OAAO;YACT;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iBAAiB;YAC/B,MAAM;QACR,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,gBAAgB,OAAO,iBAAyB;QACpD,IAAI;YACF,QAAQ,GAAG,CAAC,gCAAgC;YAC5C,aAAa;YACb,MAAM,WAAW,MAAM,oHAAA,CAAA,UAAO,CAAC,aAAa,CAAC;gBAAE;gBAAiB;YAAkB;YAClF,QAAQ,GAAG,CAAC,qCAAqC;YAEjD,wCAAmC;gBACjC,kDAAkD;gBAClD,aAAa,OAAO,CAAC,SAAS,SAAS,YAAY;gBAEnD,yEAAyE;gBACzE,MAAM,eAAe;oBACnB,aAAa,SAAS,WAAW;oBACjC,UAAU,SAAS,aAAa;oBAChC,gBAAgB;oBAChB,WAAW,SAAS,OAAO;gBAC7B;gBAEA,QAAQ,GAAG,CAAC,+BAA+B;gBAC3C,YAAY;gBACZ,QAAQ;gBAER,OAAO;YACT;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;YACxC,MAAM;QACR,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,kBAAkB,OAAO;QAC7B,IAAI;YACF,0CAA0C;YAC1C,MAAM,WAAW,MAAM,oHAAA,CAAA,WAAQ,CAAC,eAAe,CAAC;YAEhD,wCAAmC;gBACjC,kCAAkC;gBAClC,aAAa,OAAO,CAAC,SAAS,SAAS,YAAY;gBAEnD,uCAAuC;gBACvC,MAAM,uBAAuB;oBAC3B,IAAI,SAAS,eAAe;oBAC5B,UAAU,SAAS,qBAAqB;oBACxC,MAAM,SAAS,iBAAiB;gBAClC;gBAEA,wDAAwD;gBACxD,aAAa,OAAO,CAAC,oBAAoB,KAAK,SAAS,CAAC;gBACxD,oBAAoB;gBAEpB,gDAAgD;gBAChD,mBAAmB;gBACnB,aAAa,OAAO,CAAC,mBAAmB;gBAExC,QAAQ,GAAG,CAAC;gBACZ,QAAQ,GAAG,CAAC,uBAAuB,qBAAqB,QAAQ,EAAE,UAAU,qBAAqB,IAAI;gBAErG,OAAO;oBAAE,kBAAkB;gBAAqB;YAClD;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yCAAyC;YACvD,MAAM;QACR;IACF;IAEA,MAAM,SAAS;QACb,QAAQ,GAAG,CAAC;QACZ,wCAAmC;YACjC,yCAAyC;YACzC,aAAa,KAAK,GAAG,gCAAgC;;YACrD,eAAe,KAAK,GAAG,+BAA+B;;YAEtD,cAAc;YACd,QAAQ;YACR,YAAY;YACZ,mBAAmB;YACnB,oBAAoB;YAEpB,uDAAuD;YACvD,OAAO,QAAQ,CAAC,OAAO,CAAC;QAC1B;IACF;IAEA,MAAM,QAAyB;QAC7B;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;IACF;IAEA,qBACE,6LAAC,YAAY,QAAQ;QAAC,OAAO;kBAC1B;;;;;;AAGP;IAvQgB;KAAA", "debugId": null}}, {"offset": {"line": 750, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/CMS/webapp-nextjs/src/components/layout/Navbar.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect, useRef } from 'react'\nimport Link from 'next/link'\nimport { usePathname } from 'next/navigation'\nimport { Button } from '@/components/ui/button'\nimport { PrimaryButton, QuickButton } from '@/components/ui/animated-button'\nimport { Badge } from '@/components/ui/badge'\nimport { useAuth } from '@/contexts/AuthContext'\nimport {\n  Cable,\n  Home,\n  Activity,\n  BarChart3,\n  Settings,\n  Users,\n  Menu,\n  X,\n  Building2,\n  ClipboardList,\n  FileText,\n  LogOut,\n  Package,\n  ChevronDown,\n  Upload,\n  Download\n} from 'lucide-react'\n\nconst getNavigation = (userRole: string | undefined, isImpersonating: boolean, impersonatedUser: any, cantiereId?: number) => {\n  // Home button - testo personalizzato come nella webapp originale\n  const homeButton = {\n    name: userRole === 'owner' ? \"Menu Admin\" :\n          userRole === 'user' ? \"Lista Cantieri\" :\n          userRole === 'cantieri_user' ? \"Gestione Cavi\" : \"Home\",\n    href: userRole === 'owner' ? '/admin' :\n          userRole === 'user' ? '/cantieri' :\n          userRole === 'cantieri_user' ? '/cavi' : '/',\n    icon: Home\n  }\n\n  if (userRole === 'owner' && !isImpersonating) {\n    // Solo amministratore - solo il pulsante Home che va al pannello admin\n    return [homeButton]\n  }\n\n  if (userRole === 'user' || (isImpersonating && impersonatedUser?.role === 'user')) {\n    // Utente standard - Home + eventualmente cantieri se impersonificato\n    const nav = [homeButton]\n    if (isImpersonating) {\n      nav.push({ name: 'Cantieri', href: '/cantieri', icon: Building2 })\n    }\n\n    // Se un cantiere è selezionato, aggiungi i menu di gestione come nella webapp originale\n    if (cantiereId) {\n      nav.push(\n        { name: 'Visualizza Cavi', href: '/cavi', icon: Cable },\n        { name: 'Parco Cavi', href: '/parco-cavi', icon: Package },\n        { name: 'Gestione Excel', href: '/excel', icon: FileText, hasDropdown: true },\n        { name: 'Report', href: '/reports', icon: BarChart3 },\n        { name: 'Gestione Comande', href: '/comande', icon: ClipboardList },\n        { name: '⚡ Produttività', href: '/productivity', icon: Activity },\n      )\n    }\n\n    return nav\n  }\n\n  if (userRole === 'cantieri_user' || (isImpersonating && impersonatedUser?.role === 'cantieri_user')) {\n    // Utente cantiere - menu completo come nella webapp originale\n    const nav = [homeButton]\n\n    // Se un cantiere è selezionato, aggiungi i menu di gestione\n    if (cantiereId) {\n      // Se non è cantieri_user diretto, aggiungi Visualizza Cavi\n      if (userRole !== 'cantieri_user') {\n        nav.push({ name: 'Visualizza Cavi', href: '/cavi', icon: Cable })\n      }\n\n      nav.push(\n        { name: 'Parco Cavi', href: '/parco-cavi', icon: Package },\n        { name: 'Gestione Excel', href: '/excel', icon: FileText, hasDropdown: true },\n        { name: 'Report', href: '/reports', icon: BarChart3 },\n        { name: 'Gestione Comande', href: '/comande', icon: ClipboardList },\n        { name: '⚡ Produttività', href: '/productivity', icon: Activity },\n      )\n    }\n\n    return nav\n  }\n\n  // Default\n  return [homeButton]\n}\n\nexport function Navbar() {\n  const [isOpen, setIsOpen] = useState(false)\n  const [excelDropdownOpen, setExcelDropdownOpen] = useState(false)\n  const dropdownRef = useRef<HTMLDivElement>(null)\n  const pathname = usePathname()\n  const { user, cantiere, isAuthenticated, isImpersonating, impersonatedUser, logout } = useAuth()\n\n  // Recupera l'ID del cantiere selezionato dal localStorage o dal context\n  const cantiereId = cantiere?.id_cantiere || (typeof window !== 'undefined' ? parseInt(localStorage.getItem('selectedCantiereId') || '0') : 0)\n  const cantiereName = cantiere?.commessa || (typeof window !== 'undefined' ? localStorage.getItem('selectedCantiereName') : '') || `Cantiere ${cantiereId}`\n\n  const navigation = getNavigation(user?.ruolo, isImpersonating, impersonatedUser, cantiereId)\n\n  // Chiudi dropdown quando si clicca fuori\n  useEffect(() => {\n    function handleClickOutside(event: MouseEvent) {\n      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {\n        setExcelDropdownOpen(false)\n      }\n    }\n\n    document.addEventListener('mousedown', handleClickOutside)\n    return () => {\n      document.removeEventListener('mousedown', handleClickOutside)\n    }\n  }, [])\n\n  // Non mostrare navbar nella pagina di login\n  if (pathname === '/login') {\n    return null\n  }\n\n  // Se non autenticato, non mostrare navbar\n  if (!isAuthenticated) {\n    return null\n  }\n\n  return (\n    <nav className=\"fixed top-0 left-0 right-0 z-50 bg-white border-b border-slate-200 shadow-sm\">\n      <div className=\"max-w-[90%] mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex justify-between h-16\">\n\n          {/* Tutto a sinistra: Logo + Navigation */}\n          <div className=\"flex items-center space-x-6\">\n            {/* Logo e Brand */}\n            <div className=\"flex items-center space-x-3 cursor-default\">\n              <div className=\"w-8 h-8 bg-gradient-to-br from-blue-600 to-blue-700 rounded-lg flex items-center justify-center\">\n                <Cable className=\"w-5 h-5 text-white\" />\n              </div>\n              <div className=\"hidden sm:block\">\n                <h1 className=\"text-xl font-bold text-slate-900\">CABLYS</h1>\n                <p className=\"text-xs text-slate-500 -mt-1\">Cable Installation System</p>\n              </div>\n            </div>\n\n            {/* Navigation Desktop - allontanata dal logo */}\n            <div className=\"hidden md:flex items-center space-x-1\">\n            {navigation.map((item) => {\n              const isActive = pathname === item.href ||\n                             (item.href !== '/' && pathname.startsWith(item.href))\n              const Icon = item.icon\n\n              // Gestione speciale per il dropdown Excel\n              if (item.hasDropdown && item.name === 'Gestione Excel') {\n                return (\n                  <div key={item.name} className=\"relative\" ref={dropdownRef}>\n                    <Button\n                      variant=\"ghost\"\n                      size=\"sm\"\n                      className={`flex items-center space-x-2 px-3 py-2 transition-all duration-200 ease-in-out rounded-md border border-transparent ${\n                        isActive ? 'bg-blue-100 text-blue-700' : 'text-slate-600 hover:text-slate-900 hover:bg-blue-50 hover:border-blue-200'\n                      }`}\n                      onClick={() => setExcelDropdownOpen(!excelDropdownOpen)}\n                    >\n                      <Icon className=\"w-4 h-4\" />\n                      <span className=\"hidden lg:inline\">{item.name}</span>\n                      <ChevronDown className=\"w-3 h-3\" />\n                    </Button>\n\n                    {excelDropdownOpen && (\n                      <div className=\"absolute top-full left-0 mt-1 w-48 bg-white border border-slate-200 rounded-md shadow-lg z-50\">\n                        <div className=\"py-1\">\n                          <Link\n                            href=\"/excel/import\"\n                            className=\"block px-4 py-2 text-sm text-slate-700 hover:bg-slate-100\"\n                            onClick={() => setExcelDropdownOpen(false)}\n                          >\n                            <div className=\"flex items-center space-x-2\">\n                              <Upload className=\"w-4 h-4\" />\n                              <span>Importa Excel</span>\n                            </div>\n                          </Link>\n                          <Link\n                            href=\"/excel/export\"\n                            className=\"block px-4 py-2 text-sm text-slate-700 hover:bg-slate-100\"\n                            onClick={() => setExcelDropdownOpen(false)}\n                          >\n                            <div className=\"flex items-center space-x-2\">\n                              <Download className=\"w-4 h-4\" />\n                              <span>Esporta Excel</span>\n                            </div>\n                          </Link>\n                        </div>\n                      </div>\n                    )}\n                  </div>\n                )\n              }\n\n              return (\n                <Link key={item.name} href={item.href}>\n                  {isActive ? (\n                    <PrimaryButton\n                      size=\"sm\"\n                      className=\"flex items-center space-x-2\"\n                      icon={<Icon className=\"w-4 h-4\" />}\n                    >\n                      <span className=\"hidden lg:inline\">{item.name}</span>\n                    </PrimaryButton>\n                  ) : (\n                    <QuickButton\n                      size=\"sm\"\n                      className=\"flex items-center space-x-2 px-3 py-2 text-slate-600 hover:text-slate-900 hover:bg-blue-50 hover:border-blue-200 transition-all duration-200 ease-in-out rounded-md border border-transparent\"\n                    >\n                      <Icon className=\"w-4 h-4\" />\n                      <span className=\"hidden lg:inline\">{item.name}</span>\n                    </QuickButton>\n                  )}\n                </Link>\n              )\n            })}\n            </div>\n          </div>\n\n          {/* User Info a destra con più margine */}\n          <div className=\"flex items-center space-x-4 ml-8\">\n            {/* Display cantiere selezionato - versione compatta */}\n            {cantiereId && cantiereId > 0 && (\n              <div className=\"hidden sm:flex items-center space-x-2 px-2 py-1 bg-blue-50 border border-blue-200 rounded-md\">\n                <Building2 className=\"w-3 h-3 text-blue-600\" />\n                <div className=\"text-xs\">\n                  <span className=\"text-blue-900 font-medium\">{cantiereName}</span>\n                </div>\n              </div>\n            )}\n\n            <div className=\"hidden sm:flex items-center space-x-2\">\n              <div className=\"text-right\">\n                <p className=\"text-sm font-medium text-slate-900\">\n                  {isImpersonating && impersonatedUser ? impersonatedUser.username : user?.username}\n                  <span className=\"text-xs text-slate-500 ml-1\">\n                    ({user?.ruolo === 'owner' ? 'owner' : user?.ruolo || ''})\n                  </span>\n                </p>\n              </div>\n              <div className=\"w-6 h-6 bg-gradient-to-br from-green-500 to-green-600 rounded-full flex items-center justify-center\">\n                <Users className=\"w-3 h-3 text-white\" />\n              </div>\n              <Button\n                variant=\"ghost\"\n                size=\"sm\"\n                onClick={logout}\n                title={isImpersonating ? 'Torna al menu admin' : 'Logout'}\n                className=\"hover:bg-red-50 hover:text-red-600 transition-all duration-200 ease-in-out rounded-md\"\n              >\n                <LogOut className=\"w-4 h-4\" />\n              </Button>\n            </div>\n\n            {/* Mobile menu button */}\n            <div className=\"md:hidden\">\n              <Button\n                variant=\"ghost\"\n                size=\"sm\"\n                onClick={() => setIsOpen(!isOpen)}\n                className=\"text-slate-600 hover:bg-blue-50 hover:text-blue-600 transition-all duration-200 ease-in-out rounded-md\"\n              >\n                {isOpen ? <X className=\"w-5 h-5\" /> : <Menu className=\"w-5 h-5\" />}\n              </Button>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Mobile Navigation */}\n      {isOpen && (\n        <div className=\"md:hidden border-t border-slate-200 bg-white\">\n          <div className=\"px-2 pt-2 pb-3 space-y-1\">\n            {navigation.map((item) => {\n              const isActive = pathname === item.href || \n                             (item.href !== '/' && pathname.startsWith(item.href))\n              const Icon = item.icon\n              \n              return (\n                <Link key={item.name} href={item.href}>\n                  {isActive ? (\n                    <PrimaryButton\n                      size=\"sm\"\n                      className=\"w-full justify-start space-x-3\"\n                      onClick={() => setIsOpen(false)}\n                      icon={<Icon className=\"w-4 h-4\" />}\n                    >\n                      {item.name}\n                    </PrimaryButton>\n                  ) : (\n                    <QuickButton\n                      size=\"sm\"\n                      className=\"w-full justify-start space-x-3 text-slate-600 hover:text-slate-900 hover:bg-blue-50 transition-all duration-200 ease-in-out\"\n                      onClick={() => setIsOpen(false)}\n                    >\n                      <Icon className=\"w-4 h-4\" />\n                      <span>{item.name}</span>\n                    </QuickButton>\n                  )}\n                </Link>\n              )\n            })}\n          </div>\n          \n          {/* Mobile User Info - versione compatta */}\n          <div className=\"border-t border-slate-200 px-4 py-3\">\n            <div className=\"flex items-center space-x-3\">\n              <div className=\"w-6 h-6 bg-gradient-to-br from-green-500 to-green-600 rounded-full flex items-center justify-center\">\n                {user ? <Users className=\"w-3 h-3 text-white\" /> : <Building2 className=\"w-3 h-3 text-white\" />}\n              </div>\n              <div>\n                <p className=\"text-sm font-medium text-slate-900\">\n                  {isImpersonating && impersonatedUser ? impersonatedUser.username : (user ? user.username : cantiere?.commessa)}\n                  <span className=\"text-xs text-slate-500 ml-1\">\n                    ({user?.ruolo === 'owner' ? 'owner' : user?.ruolo || 'Cantiere'})\n                  </span>\n                </p>\n              </div>\n            </div>\n          </div>\n        </div>\n      )}\n    </nav>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AATA;;;;;;;;AA4BA,MAAM,gBAAgB,CAAC,UAA8B,iBAA0B,kBAAuB;IACpG,iEAAiE;IACjE,MAAM,aAAa;QACjB,MAAM,aAAa,UAAU,eACvB,aAAa,SAAS,mBACtB,aAAa,kBAAkB,kBAAkB;QACvD,MAAM,aAAa,UAAU,WACvB,aAAa,SAAS,cACtB,aAAa,kBAAkB,UAAU;QAC/C,MAAM,sMAAA,CAAA,OAAI;IACZ;IAEA,IAAI,aAAa,WAAW,CAAC,iBAAiB;QAC5C,uEAAuE;QACvE,OAAO;YAAC;SAAW;IACrB;IAEA,IAAI,aAAa,UAAW,mBAAmB,kBAAkB,SAAS,QAAS;QACjF,qEAAqE;QACrE,MAAM,MAAM;YAAC;SAAW;QACxB,IAAI,iBAAiB;YACnB,IAAI,IAAI,CAAC;gBAAE,MAAM;gBAAY,MAAM;gBAAa,MAAM,mNAAA,CAAA,YAAS;YAAC;QAClE;QAEA,wFAAwF;QACxF,IAAI,YAAY;YACd,IAAI,IAAI,CACN;gBAAE,MAAM;gBAAmB,MAAM;gBAAS,MAAM,uMAAA,CAAA,QAAK;YAAC,GACtD;gBAAE,MAAM;gBAAc,MAAM;gBAAe,MAAM,2MAAA,CAAA,UAAO;YAAC,GACzD;gBAAE,MAAM;gBAAkB,MAAM;gBAAU,MAAM,iNAAA,CAAA,WAAQ;gBAAE,aAAa;YAAK,GAC5E;gBAAE,MAAM;gBAAU,MAAM;gBAAY,MAAM,qNAAA,CAAA,YAAS;YAAC,GACpD;gBAAE,MAAM;gBAAoB,MAAM;gBAAY,MAAM,2NAAA,CAAA,gBAAa;YAAC,GAClE;gBAAE,MAAM;gBAAkB,MAAM;gBAAiB,MAAM,6MAAA,CAAA,WAAQ;YAAC;QAEpE;QAEA,OAAO;IACT;IAEA,IAAI,aAAa,mBAAoB,mBAAmB,kBAAkB,SAAS,iBAAkB;QACnG,8DAA8D;QAC9D,MAAM,MAAM;YAAC;SAAW;QAExB,4DAA4D;QAC5D,IAAI,YAAY;YACd,2DAA2D;YAC3D,IAAI,aAAa,iBAAiB;gBAChC,IAAI,IAAI,CAAC;oBAAE,MAAM;oBAAmB,MAAM;oBAAS,MAAM,uMAAA,CAAA,QAAK;gBAAC;YACjE;YAEA,IAAI,IAAI,CACN;gBAAE,MAAM;gBAAc,MAAM;gBAAe,MAAM,2MAAA,CAAA,UAAO;YAAC,GACzD;gBAAE,MAAM;gBAAkB,MAAM;gBAAU,MAAM,iNAAA,CAAA,WAAQ;gBAAE,aAAa;YAAK,GAC5E;gBAAE,MAAM;gBAAU,MAAM;gBAAY,MAAM,qNAAA,CAAA,YAAS;YAAC,GACpD;gBAAE,MAAM;gBAAoB,MAAM;gBAAY,MAAM,2NAAA,CAAA,gBAAa;YAAC,GAClE;gBAAE,MAAM;gBAAkB,MAAM;gBAAiB,MAAM,6MAAA,CAAA,WAAQ;YAAC;QAEpE;QAEA,OAAO;IACT;IAEA,UAAU;IACV,OAAO;QAAC;KAAW;AACrB;AAEO,SAAS;;IACd,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAkB;IAC3C,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,EAAE,IAAI,EAAE,QAAQ,EAAE,eAAe,EAAE,eAAe,EAAE,gBAAgB,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IAE7F,wEAAwE;IACxE,MAAM,aAAa,UAAU,eAAe,CAAC,uCAAgC,SAAS,aAAa,OAAO,CAAC,yBAAyB,2CAAQ;IAC5I,MAAM,eAAe,UAAU,YAAY,CAAC,uCAAgC,aAAa,OAAO,CAAC,8DAA4B,KAAK,CAAC,SAAS,EAAE,YAAY;IAE1J,MAAM,aAAa,cAAc,MAAM,OAAO,iBAAiB,kBAAkB;IAEjF,yCAAyC;IACzC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;4BAAE;YACR,SAAS,mBAAmB,KAAiB;gBAC3C,IAAI,YAAY,OAAO,IAAI,CAAC,YAAY,OAAO,CAAC,QAAQ,CAAC,MAAM,MAAM,GAAW;oBAC9E,qBAAqB;gBACvB;YACF;YAEA,SAAS,gBAAgB,CAAC,aAAa;YACvC;oCAAO;oBACL,SAAS,mBAAmB,CAAC,aAAa;gBAC5C;;QACF;2BAAG,EAAE;IAEL,4CAA4C;IAC5C,IAAI,aAAa,UAAU;QACzB,OAAO;IACT;IAEA,0CAA0C;IAC1C,IAAI,CAAC,iBAAiB;QACpB,OAAO;IACT;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCAGb,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,uMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;;;;;;sDAEnB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAG,WAAU;8DAAmC;;;;;;8DACjD,6LAAC;oDAAE,WAAU;8DAA+B;;;;;;;;;;;;;;;;;;8CAKhD,6LAAC;oCAAI,WAAU;8CACd,WAAW,GAAG,CAAC,CAAC;wCACf,MAAM,WAAW,aAAa,KAAK,IAAI,IACvB,KAAK,IAAI,KAAK,OAAO,SAAS,UAAU,CAAC,KAAK,IAAI;wCAClE,MAAM,OAAO,KAAK,IAAI;wCAEtB,0CAA0C;wCAC1C,IAAI,KAAK,WAAW,IAAI,KAAK,IAAI,KAAK,kBAAkB;4CACtD,qBACE,6LAAC;gDAAoB,WAAU;gDAAW,KAAK;;kEAC7C,6LAAC,qIAAA,CAAA,SAAM;wDACL,SAAQ;wDACR,MAAK;wDACL,WAAW,CAAC,mHAAmH,EAC7H,WAAW,8BAA8B,8EACzC;wDACF,SAAS,IAAM,qBAAqB,CAAC;;0EAErC,6LAAC;gEAAK,WAAU;;;;;;0EAChB,6LAAC;gEAAK,WAAU;0EAAoB,KAAK,IAAI;;;;;;0EAC7C,6LAAC,uNAAA,CAAA,cAAW;gEAAC,WAAU;;;;;;;;;;;;oDAGxB,mCACC,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;4DAAI,WAAU;;8EACb,6LAAC,+JAAA,CAAA,UAAI;oEACH,MAAK;oEACL,WAAU;oEACV,SAAS,IAAM,qBAAqB;8EAEpC,cAAA,6LAAC;wEAAI,WAAU;;0FACb,6LAAC,yMAAA,CAAA,SAAM;gFAAC,WAAU;;;;;;0FAClB,6LAAC;0FAAK;;;;;;;;;;;;;;;;;8EAGV,6LAAC,+JAAA,CAAA,UAAI;oEACH,MAAK;oEACL,WAAU;oEACV,SAAS,IAAM,qBAAqB;8EAEpC,cAAA,6LAAC;wEAAI,WAAU;;0FACb,6LAAC,6MAAA,CAAA,WAAQ;gFAAC,WAAU;;;;;;0FACpB,6LAAC;0FAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;+CAlCR,KAAK,IAAI;;;;;wCA0CvB;wCAEA,qBACE,6LAAC,+JAAA,CAAA,UAAI;4CAAiB,MAAM,KAAK,IAAI;sDAClC,yBACC,6LAAC,iJAAA,CAAA,gBAAa;gDACZ,MAAK;gDACL,WAAU;gDACV,oBAAM,6LAAC;oDAAK,WAAU;;;;;;0DAEtB,cAAA,6LAAC;oDAAK,WAAU;8DAAoB,KAAK,IAAI;;;;;;;;;;qEAG/C,6LAAC,iJAAA,CAAA,cAAW;gDACV,MAAK;gDACL,WAAU;;kEAEV,6LAAC;wDAAK,WAAU;;;;;;kEAChB,6LAAC;wDAAK,WAAU;kEAAoB,KAAK,IAAI;;;;;;;;;;;;2CAfxC,KAAK,IAAI;;;;;oCAoBxB;;;;;;;;;;;;sCAKF,6LAAC;4BAAI,WAAU;;gCAEZ,cAAc,aAAa,mBAC1B,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,mNAAA,CAAA,YAAS;4CAAC,WAAU;;;;;;sDACrB,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAK,WAAU;0DAA6B;;;;;;;;;;;;;;;;;8CAKnD,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAE,WAAU;;oDACV,mBAAmB,mBAAmB,iBAAiB,QAAQ,GAAG,MAAM;kEACzE,6LAAC;wDAAK,WAAU;;4DAA8B;4DAC1C,MAAM,UAAU,UAAU,UAAU,MAAM,SAAS;4DAAG;;;;;;;;;;;;;;;;;;sDAI9D,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,uMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;;;;;;sDAEnB,6LAAC,qIAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,MAAK;4CACL,SAAS;4CACT,OAAO,kBAAkB,wBAAwB;4CACjD,WAAU;sDAEV,cAAA,6LAAC,6MAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;;;;;;;;;;;;8CAKtB,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC,qIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS,IAAM,UAAU,CAAC;wCAC1B,WAAU;kDAET,uBAAS,6LAAC,+LAAA,CAAA,IAAC;4CAAC,WAAU;;;;;iEAAe,6LAAC,qMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAQ/D,wBACC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACZ,WAAW,GAAG,CAAC,CAAC;4BACf,MAAM,WAAW,aAAa,KAAK,IAAI,IACvB,KAAK,IAAI,KAAK,OAAO,SAAS,UAAU,CAAC,KAAK,IAAI;4BAClE,MAAM,OAAO,KAAK,IAAI;4BAEtB,qBACE,6LAAC,+JAAA,CAAA,UAAI;gCAAiB,MAAM,KAAK,IAAI;0CAClC,yBACC,6LAAC,iJAAA,CAAA,gBAAa;oCACZ,MAAK;oCACL,WAAU;oCACV,SAAS,IAAM,UAAU;oCACzB,oBAAM,6LAAC;wCAAK,WAAU;;;;;;8CAErB,KAAK,IAAI;;;;;yDAGZ,6LAAC,iJAAA,CAAA,cAAW;oCACV,MAAK;oCACL,WAAU;oCACV,SAAS,IAAM,UAAU;;sDAEzB,6LAAC;4CAAK,WAAU;;;;;;sDAChB,6LAAC;sDAAM,KAAK,IAAI;;;;;;;;;;;;+BAjBX,KAAK,IAAI;;;;;wBAsBxB;;;;;;kCAIF,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACZ,qBAAO,6LAAC,uMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;6DAA0B,6LAAC,mNAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;;;;;;8CAE1E,6LAAC;8CACC,cAAA,6LAAC;wCAAE,WAAU;;4CACV,mBAAmB,mBAAmB,iBAAiB,QAAQ,GAAI,OAAO,KAAK,QAAQ,GAAG,UAAU;0DACrG,6LAAC;gDAAK,WAAU;;oDAA8B;oDAC1C,MAAM,UAAU,UAAU,UAAU,MAAM,SAAS;oDAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUpF;GA/OgB;;QAIG,qIAAA,CAAA,cAAW;QAC2D,kIAAA,CAAA,UAAO;;;KALhF", "debugId": null}}]}