'use client'

import React from 'react'
import Link from 'next/link'
import { ArrowLeft, Cable } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { PasswordResetConfirm } from '@/components/auth/PasswordResetConfirm'

export default function ResetPasswordPage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-slate-100 flex items-center justify-center p-4">
      <div className="w-full max-w-md space-y-6">
        
        {/* Header */}
        <div className="text-center space-y-2">
          <div className="flex justify-center">
            <div className="w-16 h-16 bg-gradient-to-br from-blue-600 to-blue-700 rounded-xl flex items-center justify-center">
              <Cable className="w-8 h-8 text-white" />
            </div>
          </div>
          <h1 className="text-2xl font-bold text-slate-900">CABLYS</h1>
          <p className="text-slate-600">Reimposta Password</p>
        </div>

        {/* Password Reset Confirm Form */}
        <PasswordResetConfirm />

        {/* Back to Login */}
        <div className="text-center">
          <Link href="/login">
            <Button variant="ghost" className="text-slate-600 hover:text-slate-900">
              <ArrowLeft className="w-4 h-4 mr-2" />
              Torna al Login
            </Button>
          </Link>
        </div>

      </div>
    </div>
  )
}
