{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_0d3699dc._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_594fdf6a.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/((?!api|_next/static|_next/image|favicon.ico|public).*){(\\\\.json)}?", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico|public).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "DmFlnj8I3P1ljkWGhQVgM+UNdCE3hT3/mJhLA6xJySg=", "__NEXT_PREVIEW_MODE_ID": "c8bc6115f3ed3ae4245f5ddfc8d19b2b", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "b109d2324f41582e21703899f40d8fe81a0c19fb063d7d14205b7cc6173f12fb", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "b36cc49e5f323107303528ccd68381582da0c30f44a09decfa6887098bc98942"}}}, "instrumentation": null, "functions": {}}